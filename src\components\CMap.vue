<template>
  <div id="map-container">
    <!-- 天地图样式的折叠式地图类型切换控件 -->
    <div class="map-controls">
      <div class="map-type-switcher" @mouseenter="isExpanded = true" @mouseleave="isExpanded = false">
        <!-- 当前选中的选项（始终显示） -->
        <div class="map-type-item active current-item">
          <div class="map-preview">
            <img v-if="currentMapType === 'vector'"
                 src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAAA/CAYAAABZ9m6wAAAaAUlEQVR4nL2cV68kyZXff2Eys7KyzLXtp8dwyNmlJHJBiJAeZKAHQfqM+hgLPUiAAGFJOWCxKy5FN5xZzvT0tL22TFZlZpijh8ise293D8eRc4DqulUVGRlx4pj/Mdnq5x/9J9FKU9gKqwq2fknjVyil+eokGDXCb3LqZUOW5YzHY3zwPH/+nK7rQIFCYVWO0qB1jkTwvmVazTk+vIOgkA5EoCrnFNkYH1u0MbQbT9tuqSYVVTmFqBAEEcE5R4yC1hrnOkajEVVVkWUZxhiyLMNai3OOs7Mz2rbl7t27aG1QCkKIONfhvccWpqINNav2hCABkYDii5khb/pSC64T6kVLs+mophG/dvjg2TQrnHNobZgVtwGhbi4xEhhne0yKMdnIs45PIGQ431KMM8aF5mzzHImGkS2JXY7ROVYVRC+E6FFKISKEEBARQPDes91uERGUUgAopdBaE0IAYH9/nzzPcc4RQiTLMvK8Sgw53z4iSOi3qgB1NRE6faPM7oTfyCwRlIkoAyEEnHPYLDHX6IwsGzPL7xFCJEjHfvmAbBywVYMdeTAeQQBNJoJoWJuO8fQuVXiXjJLL5hIRj9UWrTU+eEAQAaUVGo1SICI9c9i9xxgBMMaglMI5x+npKZvNhqqqsNZycXHBZDLBvjX7CUE6gnhiDATxiDiCeEL0RBUIoSMSCNHjY9MvRBCVbqg1qJjho8X5jtxnzMojymyfotzinGc0yhG7QYoGU65RWWLETbGLKIQowshMuDN9BxNLthtHNhLyrOL4+IiyHLNablgu1yjFbpPOOYgGbTKU6H7GK+a0bUuMSSLatmW9XqO1JssysixDa41VWjHSUxRg9QirR4mzSjDKgBK23QaIoBTetyit8aHBxRZBCLTEAJVTjMUzmUyYl0dIgOAjq3iKHy/BJiZE5UEp8ApEg1KDbCZpBDZuwefLXwBCiJE4gsKMcc2nFGEMkuELITcV1mQYL+CEYjIiRsh0lqRZNCKKKIFIS4xJTdJBamKMvUpr2rbFelqEgEYhBKJs0dpgbUbTOl6+eI4qa0Yjg9EFGo3GktmMHIPWGUZPUWikNMRji7UWozM611Ht7dNGwN4jisf5FiESYiDEgAhEcYiKhOBwsUEIbNwFy+YFKOmNsIGgiU1EK43RFq0smS7QKuvV25CZESJgdYkSTaZHGF3gGti2juA0VT7D6ByRZFCVUoQQiCJY5IcELzgEwYO0RPHU6xWff/6Es/MzDu5XjKYWLS1KGRQeqyNGK4x2ZNpitMUYsMajXAcaFB1FZsntBEQRRJHlFbGzBPHkpUFrRYgerXOiRJAk5D44UCBREQnE6BEgSsDHFiQSiXShxoUGlCDiWTuXdNAnlTHKYKzFtxm+HTO1dxiNCkQM1bgkz3NCCGitybXFqmYP0D2HhaLIQEXOl49YvlxRmEPm9n1GZkqMLQKEGAlOiHgcjloCLoBIRJstqECIEaXWKOXITY6woMgje+UU5XMk5uQKtE5SYjRkxpLpEVorxtYSYofVoHRvuJUBFDFGlLIoleGDJ0aH0hoRoYsNEiNBHAqVLlaBmFmy6Yz5+BbjfIYPHqMNIQYWiyUhOpQWbOGfEUURJXmQMptQFAVuYgi3Koy2PDwqOTw+JASfxEyEGDXJ0wkhahoPLgghKupW8EGwFrYu4oIj0jAiUtkCO1JcLhacn656o90SaFB4oMZoh1aaullQzTOsAaM0xiiMBquFTAvWWDIzwiqLxVLYMSNV9P7RYFSB1hkiqne9Bq0UkRVKIlEpmnaDH12ybde42GCVUhA9EpKobusW12gIDaM8YLQwLbccTmtCkN4dD65BelM4uAlhtW44v1yQ5wXHx7fZtJEQFc6PaLaOqhrhfGCxqjFtYFxOaf24H5PUJ+jIpm3Y+gNsfkyjIz60BHGIeFA1Si9Reo3WJ0DEKN+rsGCMJzMZVmsgQQqFgOqBhFIEicTo05wk++Gjw/oQkd7SA0RRuCC4IIChmlQU5YTl2rHdthR5zma7oRyPyTPbiyX9TTV147m8XDGZCkoFRlnA6uSOXX3BZnHBcrnA1efsz2bM9wqC96A0MdLbEUOIOYLm9p1DvORs20jtNJtWs+0UXZDkOWIPEcTRhhaUQ6klolYovUTrJVo3KOVQBFAOcGgVUDqiAaU0WkGmcqz3AdBofQVkIAGs0ajge997j7IsWSxWvHx5gjGW5XLJw4f3KQ/2eyDEDupbazE2IwTwQRECRAHnIctHbLYNnQcvhm3ryRuP1gaRSIwRrRTQg67QUBUtRRH7E6b3DOAC+ABBNJ03NM6y7SrWraZxD6g7CB5iTFLlSe8iAaUatFmi9QajF2i9JuoapRy2qsZXSiAR730COCiyLKMoCmKMGGMwJi3cGH0DCQ40QOQYoa5rvPe7azKb0RrXMy9JVIyR0WhEURQsFos03hoUEGOyR89fnmNtikWKIscYg1agtSIzipGGaZlwjFaeKLKTch8gBEXnI0EyGlewahTb7oCtu5cOLQrBeZx0hOixt24d9RtRiMBqtWa5XJHnOUVRIJI2nmUWY0xv4dUuftC9dR9Ia7NjyhWjErNWqzVN0yYXlxdMpxOMUbx48QLnHPfv38UY08NtYbvd0rYtTdPu7pEOQ6NUgurG6D4WybHW9IhT7RhWZDBRyb0nFBwRCYQohCiIKHyIbJ2l7fJkVIuioOscMQYmk4okNWkTAwOuAqm4kw6RQbaupKUsxrx9/x08HVrrZHhFUFqRZZbttmFvb4+HD99iPC6JUSjLCUrBfD4DoK7XlOWI1Srn7OwcpSDLshsHMYQP3iep3mw2uzVPp1NEhKZpUEphrSXPc/LMoo3B2sQgoxRKa0aZYTJO7sIWxZi6rnn69BnT6Zjj46NdVDhsVCmFMYayHNG2HSBYm5FgtUcpS2HGIHDWPOJF83vuVD/AqtspRhJBgrC3N+/ncDjnWa02hBB6CdKsVmsuLi7Ybrd88MH7KAXOOcqyZDab7aD29TUBLBYLlsvljml1XdO2Ld578jxHa81ms9kFrVfqrTDGpljGGrI8wz579ozT01POzs55++0HNxgxvHuf8Mfh4X4vIUJmM6J4Fs0LandOVBHnay62n5PZknz2V4hElNZk2iK9TRqNxkQRkJtRaIyRZ8+ecXJy0iPJxPS9vT2UUnjvd2uLMe7s2hD+T6dTYgw453e/Dxsf9jF8VxTFzpCLCOv1emATdrVasdls8N7txHFQj0Flzs7PscZydHSItQP+0BixHFXvYbaWjy7+B+ebR9yuvs8Hh/+OaX4LHwMh+ORpYhjuiTG6N9LJwCilWC7OefH8JAV7kmxEWY4ZjcqdVFw/rOuf5/M5IQQeP368U+9BegamiAjGGKqqYjweE2Ok67pd/mTIl9hBx5RKX8QYe91PkCvXBYSMznmsGu1cn0jES0OILcvuBev2hIPyHX546z9Q2hnn289pQ83yYkNwyR0rrTHa4LqYVK1HmyJwcX7J6eKUSTVhNqpSzKS4IebXNzgwZljro0eP6LqW+XxvF+YPUjG8qqpiOp3e8JwvXrzAe89oNALAwhAGh141Iim2Sfhz7c6o/Uu225Z8vd3ZjTasqLtzVt0JdXvKXvmAHxz+W3zo+NX5f6ELG3I7Zr2C6Azz2ZzMZFzWlyzrBaO8YmSnyeU5x3pd08UNeVUxv50jRLQyr0nDdaYMJ39ycsonn3zKvXu3OTra5+JiQdM0ZJklz4seuwTyvCCEQNM0vRCoXU4kxkgIETtwcjKZMJvtkZlROhkNjV9wsvlHLtsLNpuOsPgctOBjRxfWdH5DEM/h+CHfP/zXAPzy+X9m1b3k/uyfcLv6gMo51usNhRmhRFjVS9p4ybycUeoR27hN0mKEIs+5d/cek2qcDHvvpbTWu83fNIqauq757LPPaNuGLMuoqnGf2+jIMtsbWqjrLZtNSgoNKpXslGXcR73eB+x0OumNXU41z1k0TwkxEMWzap9z2TwloIghQiywRmNNzjibYXXB3ug+x9W7NG7J707/hpV/zrQ4osoPsDpDmY7gPavVgi5saLYN4/IW1s/Y+o4QIDMlmQlkGrTkbM6FLUu0gTwveiOod3ZtAI8hBM7Ozri8vCDPM5qmIYS4w1Ap8ax6aWrwPmUFkxAklcvznP39PcpylBhyfHyIQhNxnG0ecbl5BkQ8Hu83aGUwBdgy0IYVsfcYJpbsVw+5Pf4L1psFvz3971x0nzHJ7oOHP5z8HVH/nNhMGLu3KLIKsZ755JC94i1yPaaVLRiSa4wFy+1LTk/OydWEIA5jDKPRaCfeg3q3bctms8E5R13XvQRprLUAvStXeB/wPvTXJltZ180u6Z3mGxB5TpYJVvqETBSY5feY5fcSCkSBkj6P2nE5f8zj5T9Qty/xwZG729wpfsjTk0f86tP/Ra0fsT87ojuviBJwRCbHI4IS9qsDSrmdkkA+EKLDyQYvHZnJKMuCYA1t2WKixSiNJkMkodXBgA6qct34D+pUFDn7+/u7cVprmmbD6ekZMUaKItmPIZwYxkynU7Q2dF1KQtl0fUShyU153WyBgkxrDIaD8h5KG56ufoUE4Z3jf0mhpnx0+n/ZuAtuH/6Au/vv0tmMGCJRhHuHxzRyytPL31GvL8nUFAmKKI6oIiF0jMc5e3Phfv6AO+4hJ8/O8H2J4VX3ed3DXPc0Smlu377FwcEeXeeujb7Kug9ADdjZjwHDhBBp2w6twWp184ZXOQ4FEnFxw9KvOF8942z1DOKcD+78BCMjPjn7JZ265N233+fBwV9yOLmPv9URQ+T8/IzVaoWUDV3ccOv4IceTd/DRsWheIHicCzw5+y3nTz7ir976j8zte0Q525U6FPTliT9Giul0wt27d0iROztG5XmGtWaXIryOdIeXMYa9vT0++eQTxuMCW3cXyZf3941EYnQ0fsWye8GqfU7jl6yWnm5p+Iv7/wIbZzw7fczl5ZIuCH6zxLW/4bR6TFGMqDdrPvzwN4zmkcM7Yyb2Lt47ni0+TqKqDGUx5eLkBZsLeP/tH3NYPeTlxad0LEF0crlR0Mq+VgsapKfrOvI84969OxRFRtf5G+MGdRpoQKpZZne/z2Ypflqv15Rlgf3li7/u5WKoYKR8ZIieKH0eAoVizA/u/pRb+2/x2cuPuKxfpPS/zRGJbNZbNustSmvapkOFkklRMi/2WZ0q6rDtmS54cWh9wWbRMTMPmed3ebL6Nb87/TkaQ2bHaKPJw5wZ3ycz+Q08orUiz0eMx0dYqxmPxzh3kxkDhRB3CNz7VCK5e/d2b4siy+WC8/OzFGYosO8f/it8aInE9OVOTNWuWodAZY/AlXz4+d9yVj/BqIxZcZvcjvGhpQ0bkIiVDDGGzAa22xYfO862T4jOUNgJShk0mqA6LrsznGvoXv6Bai/lW47H73Fv9s+Q6LG6YGRndJ2nbVvyPOvjnoC1qbh0PeZK0pCicEiB22RS9QGeQilNVY17sCYo5XHO03XtTt3s0fi9XTitE1be3QABUZDrnE274jdP/o6LzYudCNfdGRt3gZDSeRBpI/gg1G7Nff0eSgmFnRD64vQ0P2CSH1O7E1Z6QaciRuccTd9mxCG3Jx9QZlOkL6+aLOfp09+jNVTVEXmeA4OLvZ6LSYc4fLY24+nTJxijOT7ew9oCGMKB9NLacOfOHZxzfPjhh+m6pJNXGTCJVzqKUlhtaMOa35/9jMt6zTS7nWow2ia4Hzukr4lESa4wEGl05Gz7iAPe4eH8J7RNQySS6TFWZ2T2PjLLqTc1B+xTNkeM8xmlneJ8l8TXWtarJc+ePeP4+AitDTEK3rvem7we5xhjdoHaixcvOTraI8v2MCbbMey6C89zy/Pnz3epAjvoDihW6xWXl2tGo4zDwwOKbEzn1nx4+jecbD/GVFOU0YjSiEoJZiMBLw7TjZnIPcpshncdevYUZqc8mP+IJhRIt0zhdhR89BhjGZk9xrM9puWUdhWI2ZbgntA5t0vsrFara9X8dHCLxYK63uwCuzcxpOs6lssFt24d9iqiGKL061glhMBms2E8LimKIkkIDO0CkeVqSdflHOwfEOKWZ+vfsuxeMskP0KXBxzr5bwUpOW0osNhCk4eAVg029+xPRtw+/inT7BZdfsZ4UqDVVdCoUEyqCXlh8NFxfnZJFM+2qQkh7LBC27a9cUwqCZqm6ajreodMXyWlFG3bEkKg6xx1vdlB9YERw7gYI/P5PNV8jE0MSdxPA4s8Y1JOCNLw+PL/8Wz5IUbnu8hTiU4eqbc7aY2CZFvUaIXQUbuXoAPLVrPYPEs103yE1dmVagpUxZxJeYfNasBCVwsekkbX4Xiq3Bvy3O6q9V/EkKFZZrFY3sAn1xkyjE2Bo+HiYok1KkudOH2afzaZUxSWJ4vf8ln990SJWClopc8aqyvQBJoYW5Q2HI3f46h6l61b8GJ7gg8d5/UTUrRqknSoIccKLjbsj+5T2H9Dpg92jLqetB4WmzZ4hR0GJl1vinmVhvgmBE/bNlzPILyaThiYnecZtvN1CqWxjMsxEoVPX/6ax6u/ZX4rw8RRyggN4GiYSymGOOjW+H3+8ujfA4rfrP8rzjcYXfSGbKjxCRKT24riURiq/BbjbJ+m87us/673pE/uJOxQsb+/j7UZ3sfXMmiv0nBtVZVUVQWoPsi7kopXmde2bbJbny9/wdjuc1g9YGTnPKk/4fHJh0iR+sHClThco4RNfNwyG93hwfRHRPF8fPYzni5/TaYLENnBbrlaCYgQCdyufsA7839OYWZ09pz5fEbXOTab7Q0bEmPk7t077O/Pe9FOdaEhAr6KbdK5DWg0xsidO3f65PVqJ2Wvlk0GBqbSSIb63ZP/JrP8Dvvje0g0nC+fc7F5gtMrVB5Q8RVuKEWIHT46XNxwMHrIXvmA881jTut/RJuMV+MjiQEhAopIZJIf8Pb8p8xHd1NTXR9nbLfbpMc2lQqapiXLLLPZlCzLeulRfXZvsAuDWkEIjqbpiDFteDIZ430K7LRWlOUYazPattk1yQyJJ+99+tzWa4kSieKhByu69yBXruqmdIh4goS+68cQJBBjl4rJSr92RRTfQ2ONtTm5GaMwPZi7SmYPwdb1UsPwWzr5ISfyutimJLnj4mKxi2rLctwHeImZRTHqJWYJwHg83kW85+fnqQZVr8+/LJx8jdS1xQxqoVKHzBdfozWZTa2YqXPoqkwwqEYIgcvLS5bL5Wth/xfO2zPMOcfh4X7KzXbdLnUwJM+Vgr29fSaTyS76vd53tkO4X5cZ15lw87v4BT2b7ApF9foU5xzeh15ibhaOhmobXNmCr7SeN+CLIdWYasnpPhcXF1xeXt649tX7fCOGfF1SSuE6x2Kx2JVG30SDLg8VuG9yn5vXKa5//CpM/k4YMmS3hwTNH2PIF4Gtr0KDJ0ndC1885o/Rd8IQEIzRbzjBm/R1mfFqg65zrpeyZKzTfFcR8FeRuu+IIexc45fRYPWVfsPgodmaoac1/T30uacGXLsL8Oq6Ztuk4rzWKY6KMQ55KkBes4d/IoZcn/SLofRQQbveOv7qLNZmTKrJG2ZTQ5IVYegLsbugTWvFdDJNpdFZksjFYkXXOrTRaKXZNg3bbdPPpjA2fX99D9+aIVrZftK02h2muUYxNUEzLitGWUoPwiDq14rYCHmRsbc36zcub0w0S3/ipmdyGiQE6WhDKreKCHoUKEcKrQUIRBsQG0FdNd9onRJXQ3/sN8IhkDisVWrA9bEhSkAri9E5BrXL0A5b0MrSupbO1wipSc7FlhC7G7OKCqmBeBdyyg6zDO9DdBRfWXkUT+c3iMTUvNf3tw4M1X2DzDCnSOh/k37cN5SQoSe99uecbx6x6k7woUXrnHlxh1vj98ht1SNRdqj0vPuYs+0julATQoeL7evSJJHQP+kAN6Xj6m917d/r1Oc81asypW6OYeitf/33b8QQo3Nqd8ZHpz/jdPsHhjqODx2X+WeM832O831i2PbjM17Uv+fjs/9N61cYnaX+gr4z+cbSlcaa4g2b/Tq45OtjmIG+NkM0BiHyePH3XDafp1ZslWIFrTLKbM7ITBCuFY1IXUd7xT2WPE/h/9d6Yuvr0h+zAq/ao5v0tVY1nN7ny3/gZPMpoNHKQp91N9owK+5SZUeEcAWOYvTcm/5T5qPbvTh/dVj+pyQhEmR4hb7x16cHC2I6NJvp4itOJnjpeLL8JY8XvyDEFqMHAUtJn9LO2S/fwmpLGxoSdNZ0YcvHJ/+T0/qTlJlX3xn8AdhF1bmpyM2kfw5IkfV1H6U0VheUdob9fPXLL51QJFK7M9bdGbVLj3rd3FRvofWI0kx3uQ9IKubihtPNH3Bx+50zI0RPbksezH7MrfH3MDrjSjEURl/ZMSUK+4fz//OlkybHFXYK9yoEFkkP9UyyA8b5Pn7nSvsarF/3TXff3Nh9E0o4J/Jg+iMezH6MVmbn+d44HsEG6b5wwE3qY5HXNpUe37K6oMqPyfSINqy5vvlld0aU8IZr/7wkBLQyjLIZVuf40H2p/bJ/ChGO4snMnCrfu/G9AoI46u4koc5vENJ/GxKSy1dK96DuyzHon8D3JaAzzveZ5EcE8VyLwBJ6DPUrQOi7IRFJLWEq6wtkf3aGDOqSM8vvMLJzQnS73yB5mC5s+yL6d86S/qHJr5aOhD+BhIToyExJlR30oO3KaCkUG3eO6xHrd01CxOpR71m+Gn1rhgiRkZ1RZjPkWlyS5EFYti/+qGX/81GS3sxWWDParfbL6FtZ1NRtrJkWx4yzvZv2A0UQz7o9+Ta3+NZk+i6FL+9US/StGBLFY1VBlR1gdXFDNRTQhZbW19D/HwLfnL5KhuIN7d8SKWyB1RalhCEJt0u89ZfItcX9f8WR7jZLyeagAAAAAElFTkSuQmCC"
                 alt="地图"
                 style="background: linear-gradient(45deg, #e8f5e8 25%, transparent 25%), linear-gradient(-45deg, #e8f5e8 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e8f5e8 75%), linear-gradient(-45deg, transparent 75%, #e8f5e8 75%); background-size: 8px 8px; background-position: 0 0, 0 4px, 4px -4px, -4px 0px;">
            <img v-else
                 src="data:image/png;base64,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"
                 alt="影像"
                 style="background: linear-gradient(135deg, #4a5d23 0%, #7a8b3a 25%, #5d6e2a 50%, #3a4a1a 75%, #2a3a0a 100%);">
          </div>
          <div class="map-label">{{ currentMapType === 'vector' ? '地图' : '影像' }}</div>
        </div>

        <!-- 展开的选项列表 -->
        <div v-show="isExpanded" class="expanded-options">
          <div
            v-if="currentMapType !== 'vector'"
            class="map-type-item"
            @click="switchMapType('vector')"
          >
            <div class="map-preview">
              <img src="data:image/png;base64,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" alt="地图" style="background: linear-gradient(45deg, #e8f5e8 25%, transparent 25%), linear-gradient(-45deg, #e8f5e8 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e8f5e8 75%), linear-gradient(-45deg, transparent 75%, #e8f5e8 75%); background-size: 8px 8px; background-position: 0 0, 0 4px, 4px -4px, -4px 0px;">
            </div>
            <div class="map-label">地图</div>
          </div>

          <div
            v-if="currentMapType !== 'satellite'"
            class="map-type-item"
            @click="switchMapType('satellite')"
          >
            <div class="map-preview">
              <img src="data:image/png;base64,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" alt="影像" style="background: linear-gradient(135deg, #4a5d23 0%, #7a8b3a 25%, #5d6e2a 50%, #3a4a1a 75%, #2a3a0a 100%);">
            </div>
            <div class="map-label">影像</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

// 响应式数据
const currentMapType = ref('satellite') // 当前地图类型：satellite(影像) 或 vector(电子)
const isExpanded = ref(false) // 控制展开/折叠状态
let map = null // 地图实例

// 地图切换函数
const switchMapType = (mapType) => {
  if (!map || currentMapType.value === mapType) return

  currentMapType.value = mapType
  isExpanded.value = false // 选择后折叠

  if (mapType === 'satellite') {
    // 显示影像地图图层
    map.setLayoutProperty('影像图层1', 'visibility', 'visible')
    map.setLayoutProperty('影像图层2', 'visibility', 'visible')
    // 隐藏电子地图图层
    map.setLayoutProperty('电子图层1', 'visibility', 'none')
    map.setLayoutProperty('电子图层2', 'visibility', 'none')
  } else {
    // 隐藏影像地图图层
    map.setLayoutProperty('影像图层1', 'visibility', 'none')
    map.setLayoutProperty('影像图层2', 'visibility', 'none')
    // 显示电子地图图层
    map.setLayoutProperty('电子图层1', 'visibility', 'visible')
    map.setLayoutProperty('电子图层2', 'visibility', 'visible')
  }
}

// DOM挂载
onMounted(() => {
  mapboxgl.accessToken = 'pk.eyJ1IjoiYTEwNjcxMTE3NTYiLCJhIjoiY2tpenJpeWM1MTc0NjJxbTZnMzFhOWc3eiJ9._sYMrEMP0MT4EDmMGIRAFA'
  mapboxgl.disableTelemetry = true // 关键：禁用遥测

  map = new mapboxgl.Map({
    container: 'map-container',
    style: {
      "version": 8,
      "sources": {},
      "layers": []
    },
    zoom: 10,
    center: [107.037932, 27.72829]
  })

  map.on('load', () => {
    // 添加影像地图资源和图层
    map.addSource('影像底图资源1', {
      type: 'raster',
      tiles: ['http://t0.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '影像图层1',
      type: 'raster',
      source: '影像底图资源1',
      layout: {
        visibility: 'visible'
      }
    })

    map.addSource('影像底图资源2', {
      type: 'raster',
      tiles: ['https://t1.tianditu.gov.cn/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '影像图层2',
      type: 'raster',
      source: '影像底图资源2',
      layout: {
        visibility: 'visible'
      }
    })

    // 添加电子地图资源和图层
    map.addSource('电子底图资源1', {
      type: 'raster',
      tiles: ['http://t0.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '电子图层1',
      type: 'raster',
      source: '电子底图资源1',
      layout: {
        visibility: 'none'
      }
    })

    map.addSource('电子底图资源2', {
      type: 'raster',
      tiles: ['https://t1.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '电子图层2',
      type: 'raster',
      source: '电子底图资源2',
      layout: {
        visibility: 'none'
      }
    })
  })
})
</script>

<style scoped>
#map-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.map-controls {
  position: absolute;
  bottom: 4rem;
  right: 31rem;
  z-index: 1000;
}

.map-type-switcher {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.current-item {
  position: relative;
  z-index: 2;
}

.expanded-options {
  position: absolute;
  top: 0;
  right: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: expandLeft 0.2s ease-out;
  overflow: hidden;
  margin-right: 4px;
  display: flex;
  flex-direction: row;
}

@keyframes expandLeft {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.map-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.map-type-item:last-child {
  border-right: none;
}

.map-type-item:hover:not(.current-item) {
  background: rgba(24, 144, 255, 0.1);
}

.map-type-item.active {
  background: #1890ff;
  color: white;
}

.map-type-item.active .map-label {
  color: white;
}

.map-preview {
  width: 40px;
  height: 30px;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.map-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-label {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  text-align: center;
}
</style>
